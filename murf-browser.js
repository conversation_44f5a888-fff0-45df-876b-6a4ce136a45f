import axios from 'axios';

// Configuration
const MURF_API_KEY = 'ap2_439ce239-d996-4c89-a091-25312c44b67a';
const MURF_BASE_URL = 'https://api.murf.ai/v1';

// Create axios instance
const murfClient = axios.create({
  baseURL: MURF_BASE_URL,
  headers: {
    'api-key': MURF_API_KEY,
    'Content-Type': 'application/json',
  },
});

// Generate speech from text
async function generateSpeech(text, options = {}) {
  const {
    voiceId = 'en-US-sarah',
    format = 'mp3',
    sampleRate = 22050,
    speed = 1.0,
  } = options;

  try {
    const response = await murfClient.post(
      '/speech/generate',
      {
        text,
        voice_id: voiceId,
        format,
        sample_rate: sampleRate,
        speed,
      },
      {
        responseType: 'blob', // Use blob for browser
      },
    );

    return response.data;
  } catch (error) {
    if (error.response?.data) {
      // Handle error response in browser
      console.error('Murf API Error:', error.response.data);
    } else {
      console.error('Murf API Error:', error.message);
    }
    throw new Error(`Failed to generate speech: ${error.message}`);
  }
}

// Download audio blob as file (browser)
function downloadAudioFile(audioBlob, filename) {
  try {
    const url = URL.createObjectURL(audioBlob);
    const link = document.createElement('a');
    link.href = url;
    link.download = `${filename}.mp3`;
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
    URL.revokeObjectURL(url);
    console.log(`Audio download initiated: ${filename}.mp3`);
  } catch (error) {
    console.error('File download error:', error);
    throw error;
  }
}

// Get available voices
async function getVoices() {
  try {
    const response = await murfClient.get('/voices');
    return response.data;
  } catch (error) {
    console.error(
      'Failed to fetch voices:',
      error.response?.data || error.message,
    );
    throw error;
  }
}

// Main function - Example usage for browser
async function main() {
  try {
    // Simple text-to-speech generation
    console.log('Generating audio...');

    const audioBlob = await generateSpeech(
      'Hello! This is my personal project using Murf AI with React and axios.',
      {
        voiceId: 'en-US-sarah',
        speed: 1.1,
      },
    );

    downloadAudioFile(audioBlob, `greeting_${Date.now()}`);
    console.log('Audio generated and download initiated successfully');

    // Optional: Get available voices
    // const voices = await getVoices();
    // console.log('Available voices:', voices);
  } catch (error) {
    console.error('Error in main function:', error);
  }
}

export { generateSpeech, downloadAudioFile, getVoices, main };
