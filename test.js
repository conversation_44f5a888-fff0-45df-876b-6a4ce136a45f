import axios from 'axios';
import fs from 'fs/promises';

// Configuration
const MURF_API_KEY = 'ap2_439ce239-d996-4c89-a091-25312c44b67a';
const MURF_BASE_URL = 'https://api.murf.ai/v1';

// Create axios instance
const murfClient = axios.create({
  baseURL: MURF_BASE_URL,
  headers: {
    ,
    'Content-Type': 'application/json',
  },
});

// Generate speech from text
async function generateSpeech(text, options = {}) {
  const {
    voiceId = 'en-US-sarah',
    format = 'mp3',
    sampleRate = 22050,
    speed = 1.0,
  } = options;

  try {
    const response = await murfClient.post(
      '/speech/generate',
      {
        text,
        voice_id: voiceId,
        format,
        sample_rate: sampleRate,
        speed,
      },
      {
        responseType: 'arraybuffer',
      },
    );

    return response.data;
  } catch (error) {
    console.error('Murf API Error:', error.response?.data || error.message);
    throw new Error(`Failed to generate speech: ${error.message}`);
  }
}

// Save audio buffer to file
async function saveAudioFile(audioBuffer, filename) {
  try {
    // Create audio directory if it doesn't exist
    await fs.mkdir('./audio', { recursive: true });

    const outputPath = `./audio/${filename}.mp3`;
    await fs.writeFile(outputPath, audioBuffer);
    console.log(`Audio saved to: ${outputPath}`);
    return outputPath;
  } catch (error) {
    console.error('File save error:', error);
    throw error;
  }
}

// Get available voices
async function getVoices() {
  try {
    const response = await murfClient.get('/voices');
    return response.data;
  } catch (error) {
    console.error(
      'Failed to fetch voices:',
      error.response?.data || error.message,
    );
    throw error;
  }
}

// Main function - Example usage
async function main() {
  try {
    // Simple text-to-speech generation
    console.log('Generating audio...');

    const audioBuffer = await generateSpeech(
      'Hello! This is my personal project using Murf AI with Node.js and axios.',
      {
        voiceId: 'en-US-sarah',
        speed: 1.1,
      },
    );

    const filePath = await saveAudioFile(audioBuffer, `greeting_${Date.now()}`);
    console.log('Audio generated successfully:', filePath);

    // Optional: Get available voices
    // const voices = await getVoices();
    // console.log('Available voices:', voices);
  } catch (error) {
    console.error('Error in main function:', error);
  }
}

main();
