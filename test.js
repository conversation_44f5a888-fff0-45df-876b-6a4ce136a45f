import axios from 'axios';
import fs from 'fs/promises';
import dotenv from 'dotenv';

dotenv.config();

// Configuration
const MURF_API_KEY = process.env.MURF_API_KEY;
const MURF_BASE_URL = 'https://api.murf.ai';

// Create axios instance (we'll update headers after getting token)
const murfClient = axios.create({
  baseURL: MURF_BASE_URL,
  headers: {
    'Content-Type': 'application/json',
  },
});

// Generate auth token first
async function generateAuthToken() {
  try {
    const response = await murfClient.post('/v1/auth/token', {
      apiKey: MURF_API_KEY,
    });

    return response.data.token;
  } catch (error) {
    console.error(
      'Auth token generation failed:',
      error.response?.data || error.message,
    );
    throw new Error(`Failed to generate auth token: ${error.message}`);
  }
}

// Generate speech from text
async function generateSpeech(token, text, options = {}) {
  const {
    voiceId = 'en-US-sarah',
    format = 'MP3',
    sampleRate = 22050,
    speed = 1.0,
    model = 'GEN2',
  } = options;

  try {
    const response = await murfClient.post(
      '/v1/speech/generate',
      {
        text,
        voiceId,
        format,
        sampleRate,
        speed,
        model,
      },
      {
        headers: {
          token: token,
        },
        responseType: 'json', // Get URL first, then download
      },
    );

    // If the response contains a URL, fetch the audio
    if (response.data.audioUrl) {
      const audioResponse = await axios.get(response.data.audioUrl, {
        responseType: 'arraybuffer',
      });
      return audioResponse.data;
    } else if (response.data.base64Audio) {
      // If base64 encoded audio is returned
      return Buffer.from(response.data.base64Audio, 'base64');
    } else {
      throw new Error('No audio data received from API');
    }
  } catch (error) {
    if (error.response?.data) {
      console.error('Murf API Error:', error.response.data);
    } else {
      console.error('Murf API Error:', error.message);
    }
    throw new Error(`Failed to generate speech: ${error.message}`);
  }
}

// Save audio buffer to file
async function saveAudioFile(audioBuffer, filename) {
  try {
    await fs.mkdir('./audio', { recursive: true });

    const outputPath = `./audio/${filename}.mp3`;
    await fs.writeFile(outputPath, audioBuffer);
    console.log(`Audio saved to: ${outputPath}`);
    return outputPath;
  } catch (error) {
    console.error('File save error:', error);
    throw error;
  }
}

// Get available voices
async function getVoices(token) {
  try {
    const response = await murfClient.get('/v1/speech/voices', {
      headers: {
        token: token,
      },
    });
    return response.data;
  } catch (error) {
    console.error(
      'Failed to fetch voices:',
      error.response?.data || error.message,
    );
    throw error;
  }
}

// Main function
async function main() {
  try {
    // Check if API key is set
    if (!MURF_API_KEY) {
      console.error('Please set MURF_API_KEY in your .env file');
      return;
    }

    console.log('Generating auth token...');
    const token = await generateAuthToken();
    console.log('Auth token generated successfully');

    console.log('Generating audio...');
    const audioBuffer = await generateSpeech(
      token,
      'Hello! This is my personal project using Murf AI with Node.js and axios.',
      {
        voiceId: 'en-US-sarah',
        speed: 1.1,
        model: 'GEN2',
      },
    );

    const filePath = await saveAudioFile(audioBuffer, `greeting_${Date.now()}`);
    console.log('Audio generated successfully:', filePath);

    // Optional: Get available voices
    console.log('Fetching available voices...');
    const voices = await getVoices(token);
    console.log('Available voices:', voices.slice(0, 5)); // Show first 5 voices
  } catch (error) {
    console.error('Error in main function:', error);
  }
}

// Run the main function
main();

// Export functions if needed
export { generateAuthToken, generateSpeech, saveAudioFile, getVoices };
